using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Klee.Domain.Messages.Queries.InvoiceManagement.Invoices;
using Klee.Domain.Messages.Queries.InvoiceManagement.Invoices.Data;
using Klee.Domain.Services.Repositories.InvoiceManagement;
using Klee.Domain.Services.Repositories.VoyageManagement;
using Microsoft.EntityFrameworkCore;
using Paramore.Brighter;
using Paramore.Darker;

namespace Klee.Domain.Services.QueryHandlers.InvoiceManagement.Invoices
{
    public class GetOrganizationInvoiceStatisticsQueryHandler
        : QueryHandlerAsync<GetOrganizationInvoiceStatisticsQuery, InvoiceStatistics>
    {
        #region PROPERTIES
        private IVoyageInvoiceSrpRepository VoyageInvoiceSrpRepository { get; }
        private IVoyageSrpRepository VoyageSrpRepository { get; }
        #endregion

        #region CONSTRUCTORS
        public GetOrganizationInvoiceStatisticsQueryHandler(
            IVoyageInvoiceSrpRepository voyageInvoiceSrpRepository,
            IVoyageSrpRepository voyageSrpRepository)
        {
            VoyageInvoiceSrpRepository = voyageInvoiceSrpRepository;
            VoyageSrpRepository = voyageSrpRepository;
        }
        #endregion

        #region METHODS
        public override async Task<InvoiceStatistics> ExecuteAsync(GetOrganizationInvoiceStatisticsQuery query,
            CancellationToken cancellationToken = new ())
        {
            try {

                string organizationId = query.OrganizationId;
                DateTime startOfMonth = new DateTime(DateTime.UtcNow.Year, DateTime.UtcNow.Month, 1).ToUniversalTime();
                DateTime endOfMonth = startOfMonth.AddMonths(1).ToUniversalTime();

                // Get voyages posted by organization this month
                int voyagesPostedThisMonth = await VoyageSrpRepository.Entities(query)
                    .Where(v => v.BookingOrganizationId == organizationId &&
                                v.CreatedDateTimeUtc >= startOfMonth &&
                                v.CreatedDateTimeUtc < endOfMonth)
                    .CountAsync(cancellationToken);

                // Get total hours of voyages this month for organization
                // Use client evaluation for complex calculations
                List<(DateTime Start, DateTime End)> voyageTimes = await VoyageSrpRepository.Entities(query)
                    .Where(v => v.BookingOrganizationId == organizationId &&
                                v.StartDateTime >= startOfMonth &&
                                v.StartDateTime < endOfMonth)
                    .Select(v => new { v.StartDateTime, v.EndDateTime })
                    .ToListAsync(cancellationToken)
                    .ContinueWith(task => task.Result.Select(v => (v.StartDateTime, v.EndDateTime)).ToList(),
                        cancellationToken);

                double totalHoursThisMonth = voyageTimes.Sum(v => (v.End - v.Start).TotalHours);

                // Get money earned this month (incoming invoices - where organization provides operator)
                double moneyEarnedThisMonth = await VoyageInvoiceSrpRepository.Entities(query)
                    .Where(vi => vi.OperatorOrganizationId == organizationId &&
                                 vi.CreatedDateTimeUtc >= startOfMonth &&
                                 vi.CreatedDateTimeUtc < endOfMonth)
                    .SumAsync(vi => vi.TotalAmountInEuros, cancellationToken);

                // Get total money earned (all time incoming invoices)
                double totalMoneyEarned = await VoyageInvoiceSrpRepository.Entities(query)
                    .Where(vi => vi.OperatorOrganizationId == organizationId)
                    .SumAsync(vi => vi.TotalAmountInEuros, cancellationToken);

                return new InvoiceStatistics {
                    VoyagesPostedThisMonth = voyagesPostedThisMonth,
                    TotalHoursThisMonth = totalHoursThisMonth,
                    MoneyEarnedThisMonth = moneyEarnedThisMonth,
                    TotalMoneyEarned = totalMoneyEarned
                };
            }
            catch (Exception ex)
            {
                // Log exception (if logging is set up)
                // For now, just rethrow
                throw new InvalidOperationException("Error calculating invoice statistics", ex);
            }
        }
        #endregion
    }
}
