﻿using Klee.Domain.Messages.Queries.OperatorManagement.Operators.Data;
using Klee.Domain.Messages.Queries.OperatorManagement.Operators;
using Klee.Domain.Services.Application.Caching.MemoryCaching;
using Klee.Domain.Services.Repositories.OperatorManagement;
using Microsoft.Extensions.Caching.Memory;
using Paramore.Darker;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Threading;
using System;
using System.Linq;
using Microsoft.EntityFrameworkCore;

namespace Klee.Domain.Services.QueryHandlers.OperatorManagement.Operators;

   public sealed class GetOperatorListQueryHandler
        : QueryHandlerAsync<GetOperatorListQuery, IReadOnlyList<OperatorListItem>>
    {
        #region PROPERTIES
        private IOperatorSrpRepository OperatorSrpRepository { get; }
        private IMemoryCache MemoryCache { get; }
        #endregion

        #region CONSTRUCTORS
        public GetOperatorListQueryHandler(IOperatorSrpRepository operatorSrpRepository,
                                          IMemoryCache memoryCache)
        {
            this.OperatorSrpRepository = operatorSrpRepository;
            this.MemoryCache = memoryCache;
        }
        #endregion

        #region METHODS
        public override async Task<IReadOnlyList<OperatorListItem>> ExecuteAsync(GetOperatorListQuery query,
                                                                                CancellationToken cancellationToken = new CancellationToken())
        {
            try {


                // Get OperatorListItems from cache
                var isCached = this.MemoryCache.TryGetValue(MemoryCacheIds.GetOperatorListCacheId,
                    out List<OperatorListItem> cachedOperatorListItems);

                // Init
                List<OperatorListItem> operatorListItems = cachedOperatorListItems ?? new List<OperatorListItem>();

                // Get OperatorListItems from DB (if needed)
                if (query.AllowCached == false ||
                    !isCached) {
                    // Get OperatorList Items
                    operatorListItems =
                        await this.OperatorSrpRepository
                            .Entities(query)
                            .OrderBy(_ => _.OperatorId)
                            .Select(_ => new OperatorListItem() {
                                EntityId = _.EntityId,
                                OperatorId = _.OperatorId,
                                OperatorFirstName = _.FirstName,
                                OperatorLastName = _.LastName,
                                Email = _.OperatorEmail,
                                HourlyRateInEuros = _.HourlyRateInEuros,
                                OrganizationId = _.OrganizationId,
                                IsActive = _.IsActive ?? false
                            })
                            .ToListAsync(cancellationToken: cancellationToken);

                    // Cache VehicleListItems
                    this.MemoryCache.Set(MemoryCacheIds.GetOperatorListCacheId,
                        operatorListItems, new MemoryCacheEntryOptions() {
                            AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(1),
                            Size = 1
                        });
                }

                // Filter "IsActive" (if needed)
                if (query.IsActive != null) {
                    operatorListItems = operatorListItems.Where(_ => _.IsActive == query.IsActive).ToList();
                }

                return operatorListItems;
            }
        catch (Exception ex)
        {
            throw new Exception($"Error while getting operator list: {ex.Message}", ex);
        }
    }
        #endregion
    }