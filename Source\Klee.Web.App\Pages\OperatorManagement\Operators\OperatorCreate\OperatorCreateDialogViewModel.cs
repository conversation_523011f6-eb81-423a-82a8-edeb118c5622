﻿using EnumsNET;
using Klee.Domain.Messages.Commands.OperatorManagement.Operators.Validators;
using Klee.Domain.Messages.Commands.OperatorManagement.Operators;
using Klee.Domain.Messages.Queries.OrganizationManagement.Organizations.Data;
using Klee.Domain.Messages.Queries.OrganizationManagement.Organizations;
using Renoir.SoftwareEnvironments;
using Renoir.Srp.Portal.Web.Pages.Common;
using System.ComponentModel.DataAnnotations;
using Klee.Domain.Entities.QualificationManagement.Qualifications.Data;
using Monet.Helpers;
using Renoir.Application.Validations.Helpers;


namespace Klee.Web.App.Pages.OperatorManagement.Operators.OperatorCreate;

public class OperatorCreateDialogViewModel
    : ViewModelBase<OperatorCreateDialogViewModel>, IValidatableObject
{
    #region FIELDS
    private string _softwareEnvironmentDisplayName = SoftwareEnvironmentIds.Prod.GetDisplayName();
    private SoftwareEnvironmentIds _softwareEnvironmentId = SoftwareEnvironmentIds.Prod;


    #endregion

    #region PROPERTIES - STATIC
    // Selectable
    public static IList<string> SelectableSoftwareEnvironmentDisplayNames { get; } = Enums.GetMembers<SoftwareEnvironmentIds>()
                                                                                          .Where(_ => _.Value != SoftwareEnvironmentIds.None)
                                                                                          .Select(_ => _.AsString(EnumFormat.DisplayName))
                                                                                          .OrderBy(_ => _).ToList();

    public static IList<KeyValuePair<QualificationTypeIds, string>> SelectableOperatorQualificationsWithDisplayNames { get; }
        = Enums.GetMembers<QualificationTypeIds>()
            .Where(_ => _.Value != QualificationTypeIds.None && _.Value != QualificationTypeIds.Undefined)
            .Select(_ => new KeyValuePair<QualificationTypeIds,string>(
                _.Value,
                _.Value.GetDisplayName()))
            .ToList();
    #endregion

    #region PROPERTIES

    public IReadOnlyList<OrganizationNameListItem> OrganizationNames { get; set; } = new List<OrganizationNameListItem>();

    public IEnumerable<QualificationTypeIds> QualificationsSelected { get; set; } = new List<QualificationTypeIds>();

    [Required]
    [Display(Name = "Software Environment")]
    public string SoftwareEnvironmentDisplayName
    {
        get => _softwareEnvironmentDisplayName;
        set => SetSoftwareEnvironmentDisplayName(value);
    }


    [Display(Name = "First Name")]
    public string OperatorFirstName { get; set; } = "";

    [Display(Name = "Last Name")]
    public string OperatorLastName { get; set; } = "";

    [Display(Name = "Email")]
    public string OperatorEmail { get; set; } = "";

    [Display(Name = "Hourly rate (Euro)")]
    public double HourlyRateInEuros { get; set; } = 0.0;

    [Required]
    [Display(Name = "Organization Name")]
    public string SelectedOrganizationId { get; set; } = "";
    #endregion

    #region PROPERTIES - CONVERTED
    public SoftwareEnvironmentIds SoftwareEnvironmentId => _softwareEnvironmentId;
    #endregion

    #region CONSTRUCTORS
    public OperatorCreateDialogViewModel(IViewModelHost viewModelHost)
        : base(viewModelHost)
    {
    }
    #endregion

    #region METHODS
    public void Clear()
    {
        SoftwareEnvironmentDisplayName = SoftwareEnvironmentIds.Prod.GetDisplayName();
        OperatorFirstName = "";
        OperatorLastName = "";
        OperatorEmail = "";
        HourlyRateInEuros = 0.0;
        OrganizationNames = new List<OrganizationNameListItem>();
        QualificationsSelected = new List<QualificationTypeIds>();
        SelectedOrganizationId = "";
    }

    private void SetSoftwareEnvironmentDisplayName(string softwareEnvironmentDisplayName)
    {
        // Init
        bool isSoftwareEnvironmentChanged = softwareEnvironmentDisplayName != SoftwareEnvironmentDisplayName;

        //
        if (Enums.TryParse(softwareEnvironmentDisplayName, true, out SoftwareEnvironmentIds enumValue, EnumFormat.DisplayName))
        {
            _softwareEnvironmentDisplayName = softwareEnvironmentDisplayName;
            _softwareEnvironmentId = enumValue;
        }
        else
        {
            _softwareEnvironmentDisplayName = softwareEnvironmentDisplayName;
            _softwareEnvironmentId = SoftwareEnvironmentIds.None;
        }

        // Handle changed software environment type
        if (isSoftwareEnvironmentChanged)
        {
            //
        }

        //
        InvokeStateHasChangedOnHost();
    }
    #endregion

    #region METHODS - VALIDATE
    public IEnumerable<ValidationResult> Validate(ValidationContext validationContext)
    {
        // Init
        List<ValidationResult> validationResults = new List<ValidationResult>();

        // Validate Command
        CreateOperatorCommandValidator createOperatorCommandValidator = new CreateOperatorCommandValidator();
        createOperatorCommandValidator.Validate(NewCreateOperatorCommand())
                                     .AddTo(validationResults);

        return validationResults;
    }
    #endregion

    #region METHODS
    public async Task<bool> LoadOrganizationNamesAsync()
    {

        // Load organizations
        IReadOnlyList<OrganizationNameListItem>? organizationNameList = await SrpQueryProcessor.ExecuteAsync(new GetOrganizationNameListQuery(SrpQueryContext)
        {
            IsActive = false
        });

        OrganizationNames = organizationNameList
            .OrderBy(orgListItem => orgListItem.Name)
            .ToList();

        // Notify
        await InvokeStateHasChangedOnHostAsync();

        return true;
    }
    private CreateOperatorCommand NewCreateOperatorCommand()
    {

        return new CreateOperatorCommand(SoftwareEnvironmentId,
                                        SelectedOrganizationId,
                                        SrpCommandContext)
        {
            OperatorFirstName = OperatorFirstName,
            OperatorLastName = OperatorLastName,
            OperatorEmail = OperatorEmail,
            HourlyRateInEuros = HourlyRateInEuros,
            Qualifications = QualificationsSelected.ToList()
        };
    }

    public async Task CreateOperatorAsync()
    {
        // Init
        CreateOperatorCommand createOperatorCommand = NewCreateOperatorCommand();

        // Create UserProfile
        await SrpCommandProcessor
                  .SendAsync(createOperatorCommand, false);
    }
    #endregion
}