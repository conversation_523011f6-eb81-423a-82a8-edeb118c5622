﻿using EnumsNET;
using Renoir.Srp.Portal.Web.Pages.Common;
using System.ComponentModel.DataAnnotations;
using Klee.Domain.Messages.Commands.RocManagement.Rocs;
using Klee.Domain.Messages.Commands.RocManagement.Rocs.Validators;
using Klee.Domain.Messages.Queries.OrganizationManagement.Organizations;
using Klee.Domain.Messages.Queries.OrganizationManagement.Organizations.Data;
using Monet.Helpers;
using Renoir.Application.Validations.Helpers;
using Renoir.SoftwareEnvironments;

namespace Klee.Web.App.Pages.RocManagement.Rocs.RocCreate;

    public class RocCreateDialogViewModel
        : ViewModelBase<RocCreateDialogViewModel>, IValidatableObject
    {
        #region FIELDS
        private string _softwareEnvironmentDisplayName = SoftwareEnvironmentIds.Prod.GetDisplayName();
        private SoftwareEnvironmentIds _softwareEnvironmentId = SoftwareEnvironmentIds.Prod;
        #endregion

        #region PROPERTIES - STATIC
        // Selectable
        public static IList<string> SelectableSoftwareEnvironmentDisplayNames { get; } = Enums.GetMembers<SoftwareEnvironmentIds>()
                                                                                              .Where(_ => _.Value != SoftwareEnvironmentIds.None)
                                                                                              .Select(_ => _.AsString(EnumFormat.DisplayName))
                                                                                              .OrderBy(_ => _).ToList();
        #endregion

        #region PROPERTIES
        public IReadOnlyList<OrganizationNameListItem> OrganizationNames { get; set; } = new List<OrganizationNameListItem>();

        [Required]
        [Display(Name = "Software Environment")]
        public string SoftwareEnvironmentDisplayName
        {
            get => this._softwareEnvironmentDisplayName;
            set => this.SetSoftwareEnvironmentDisplayName(value);
        }


        [Required]
        [Display(Name = "ROC Name")]
        public string RocName { get; set; } = "";

        [Required]
        [Display(Name = "ROC Address")]
        public string RocAddress { get; set; } = "";

        [Required]
        [Display(Name = "Organization Name")]
        public string SelectedOrganizationId { get; set; } = "";
        #endregion

        #region PROPERTIES - CONVERTED
        public SoftwareEnvironmentIds SoftwareEnvironmentId => this._softwareEnvironmentId;
        #endregion

        #region CONSTRUCTORS
        public RocCreateDialogViewModel(IViewModelHost viewModelHost)
            : base(viewModelHost)
        {
        }
        #endregion

        #region METHODS
        public async Task<bool> LoadOrganizationNamesAsync()
        {
            // Load organizations
            this.OrganizationNames = await this.SrpQueryProcessor.ExecuteAsync(new GetOrganizationNameListQuery(this.SrpQueryContext)
            {
                IsActive = false
            });

            this.OrganizationNames = this.OrganizationNames
                .OrderBy(orgListItem => orgListItem.Name)
                .ToList();

            // Notify
            await this.InvokeStateHasChangedOnHostAsync();

            return true;
        }
        public void Clear()
        {
            this.SoftwareEnvironmentDisplayName = SoftwareEnvironmentIds.Prod.GetDisplayName();
            this.RocName = "";
            this.RocAddress = "";
            this.OrganizationNames = new List<OrganizationNameListItem>();
            this.SelectedOrganizationId = "";
    }

        private void SetSoftwareEnvironmentDisplayName(string softwareEnvironmentDisplayName)
        {
            // Init
            bool isSoftwareEnvironmentChanged = softwareEnvironmentDisplayName != this.SoftwareEnvironmentDisplayName;

            //
            if (Enums.TryParse(softwareEnvironmentDisplayName, true, out SoftwareEnvironmentIds enumValue, EnumFormat.DisplayName))
            {
                this._softwareEnvironmentDisplayName = softwareEnvironmentDisplayName;
                this._softwareEnvironmentId = enumValue;
            }
            else
            {
                this._softwareEnvironmentDisplayName = softwareEnvironmentDisplayName;
                this._softwareEnvironmentId = SoftwareEnvironmentIds.None;
            }

            // Handle changed software environment type
            if (isSoftwareEnvironmentChanged)
            {
                //
            }

            //
            this.InvokeStateHasChangedOnHost();
        }

        #endregion

        #region METHODS - VALIDATE
        public IEnumerable<ValidationResult> Validate(ValidationContext validationContext)
        {
            // Init
            List<ValidationResult> validationResults = new List<ValidationResult>();

            // Validate Command
            CreateRocCommandValidator createRocCommandValidator = new CreateRocCommandValidator();
            createRocCommandValidator.Validate(this.NewCreateRocCommand())
                                         .AddTo(validationResults);

            return validationResults;
        }
        #endregion

        #region METHODS
        private CreateRocCommand NewCreateRocCommand()
        {
            return new CreateRocCommand(this.SoftwareEnvironmentId,
                                            this.SelectedOrganizationId,
                                            this.SrpCommandContext)
                   {
                       RocName = this.RocName,
                       RocAddress = this.RocAddress,
                   };
        }

        public async Task CreateRocAsync()
        {
            // Init
            CreateRocCommand createRocCommand = this.NewCreateRocCommand();

            // Create UserProfile
            await this.SrpCommandProcessor
                      .SendAsync(createRocCommand, false);

        }
        #endregion
    }